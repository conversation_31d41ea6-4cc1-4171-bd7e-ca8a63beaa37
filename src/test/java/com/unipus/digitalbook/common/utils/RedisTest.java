package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.model.constants.CacheConstant;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.stream.IntStream;

@SpringBootTest
public class RedisTest {
    private static final long   INSTANCE_CACHE_SECONDS   = CacheConstant.REDIS_USER_PAPER_INSTANCE_TIMEOUT_HOURS * 3600L;

    @Resource
    private RedisUtil redisUtil;

    @Test
    public void test(){
        IntStream.range(1, 100000).parallel().forEach(this::cachePaperInstance);
    }

    public void cachePaperInstance(Integer n) {

        String instanceKey = "PAPER_INSTANCE:FFFF-FFFF-FFFF-FFFF:" + n;
        redisUtil.writeToBucket(instanceKey, JsonUtil.toJsonString(content), INSTANCE_CACHE_SECONDS);
    }

    public static final String content =
            """
                    {
                      "root": {
                        "children": [
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H1",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "BXV47lEH24jeByXf",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h1",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H2",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "zobBV7f7cSCJmPZT",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h2",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H3",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "z2Lpq67B5DRKXCHt",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h3",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H4",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "Sk5njiH15JfqmIZt",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h4",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H5",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "vut7WllGeD5vDNRP",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h5",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "children": [
                              {
                                "detail": 0,
                                "format": 0,
                                "mode": "normal",
                                "style": "",
                                "text": "H6",
                                "type": "text",
                                "version": 1
                              }
                            ],
                            "direction": "ltr",
                            "format": "",
                            "indent": 0,
                            "type": "heading",
                            "version": 1,
                            "$": {
                              "__anchor__": "jZwqjJ1HGgYVJmHH",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "tag": "h6",
                            "style": "",
                            "theme": "",
                            "themePayload": {}
                          },
                          {
                            "type": "question-block",
                            "version": 1,
                            "$": {
                              "__anchor__": "spPpUoLxxI4sYZl0",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "questionType": "single_choice_group",
                            "questionData": "\\"spPpUoLxxI4sYZl0==udm0oVzu4oWrKKON\\"",
                            "questionGroup": "spPpUoLxxI4sYZl0==udm0oVzu4oWrKKON",
                            "style": "",
                            "questionState": null
                          },
                          {
                            "type": "question-block",
                            "version": 1,
                            "$": {
                              "__anchor__": "WabkSk0GkKwGTepw",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "questionType": "fill_blanks_group",
                            "questionData": "\\"WabkSk0GkKwGTepw==tzvLetDEs1bAgGHX\\"",
                            "questionGroup": "WabkSk0GkKwGTepw==tzvLetDEs1bAgGHX",
                            "style": "",
                            "questionState": null
                          },
                          {
                            "type": "question-block",
                            "version": 1,
                            "$": {
                              "__anchor__": "rvGSHonDUBN9gqkm",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "questionType": "writing_group",
                            "questionData": "\\"rvGSHonDUBN9gqkm==Xm3xCBxRujBD2UrP\\"",
                            "questionGroup": "rvGSHonDUBN9gqkm==Xm3xCBxRujBD2UrP",
                            "style": "",
                            "questionState": null
                          },
                          {
                            "children": [],
                            "direction": null,
                            "format": "",
                            "indent": 0,
                            "type": "base-paragraph",
                            "version": 1,
                            "$": {
                              "__anchor__": "oE4Pv5JQO1i303Qi",
                              "__anchor__scope__": "wx1jxTmpwKLMuxuG"
                            },
                            "textFormat": 0,
                            "textStyle": "",
                            "style": ""
                          }
                        ],
                        "direction": "ltr",
                        "format": "",
                        "indent": 0,
                        "type": "root",
                        "version": 1
                      }
                    }
            """;
}
