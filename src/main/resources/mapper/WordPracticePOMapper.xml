<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.WordPracticePOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.WordPracticePO">
        <id property="id" column="id"/>
        <result property="bizId" column="biz_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="enable" column="enable"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_id,parent_id,`name`,create_time,update_time,create_by,
        update_by,`enable`
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice
        where id = #{id}
    </select>

    <select id="selectList" resultType="com.unipus.digitalbook.model.po.WordPracticePO">
        select t1.id,
               t1.biz_id      as bizId,
               t1.parent_id   as parentId,
               t1.`name`,
               t1.update_time as createTime,
               t2.name        as createUserName
        from word_practice t1
        left join user_info t2 on t1.update_by = t2.id
        where t1.parent_id = #{parentId}
        order by t1.create_time
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        select count(*)
        from word_practice
        where parent_id = #{parentId}
    </select>

    <select id="selectListByParentIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice
        where parent_id in
        <foreach collection="parentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by create_time desc
    </select>

    <select id="selectByBizId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from word_practice
        where biz_id = #{bizId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from word_practice
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.WordPracticePO"
            useGeneratedKeys="true">
        insert into word_practice
        ( id,biz_id,parent_id,name,create_time,update_time,
          create_by,update_by,enable)
        values (#{id},#{bizId},#{parentId},#{name},#{createTime},#{updateTime},
                #{createBy},#{updateBy},#{enable})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.WordPracticePO" useGeneratedKeys="true">
        insert into word_practice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null">#{name},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="enable != null">#{enable},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.WordPracticePO">
        update word_practice
        <set>
            <if test="bizId != null">
                biz_id = #{bizId},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.WordPracticePO">
        update word_practice
        set biz_id      = #{bizId},
            parent_id  = #{parentId},
            name        = #{name},
            create_time = #{createTime},
            update_time = #{updateTime},
            create_by   = #{createBy},
            update_by   = #{updateBy},
            enable      = #{enable}
        where id = #{id}
    </update>

    <select id="selectByName" resultType="java.lang.Integer">
        select
            count(*)
        from word_practice
        where parent_id = #{parentId}
        and name = #{name}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>
