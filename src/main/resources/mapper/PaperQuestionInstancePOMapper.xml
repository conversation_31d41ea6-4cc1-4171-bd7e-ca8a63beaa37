<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper">

    <resultMap id="BaseResultMap" type="PaperQuestionInstancePO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_group_id" property="questionGroupId" jdbcType="BIGINT"/>
        <result column="question_biz_group_id" property="questionBizGroupId" jdbcType="CHAR"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="question_biz_id" property="questionBizId" jdbcType="CHAR"/>
        <result column="round_id" property="roundId" jdbcType="VARCHAR"/>
        <result column="user_score" property="userScore" jdbcType="DECIMAL"/>
        <result column="standard_score" property="standardScore" jdbcType="DECIMAL"/>
        <result column="is_scored" property="isScored" jdbcType="BIT"/>
        <result column="is_judged" property="isJudged" jdbcType="BIT"/>
        <result column="is_correct" property="isCorrect" jdbcType="BIT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, question_group_id, question_biz_group_id, question_id, question_biz_id,
        round_id, user_score, standard_score, is_scored, is_judged, is_correct, create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO paper_question_instance (
            question_group_id, question_biz_group_id, question_id, question_biz_id,
            round_id, user_score, standard_score, is_scored, is_judged, is_correct, create_by, update_by, enable
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.questionGroupId},
            #{item.questionBizGroupId},
            #{item.questionId},
            #{item.questionBizId},
            #{item.roundId},
            #{item.userScore},
            #{item.standardScore},
            #{item.isScored},
            #{item.isJudged},
            #{item.isCorrect},
            #{item.createBy},
            #{item.updateBy},
            #{item.enable}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
        user_score = VALUES(user_score),
        standard_score = VALUES(standard_score),
        is_scored = VALUES(is_scored),
        is_judged = VALUES(is_judged),
        is_correct = VALUES(is_correct),
        update_by = VALUES(update_by),
        enable = VALUES(enable)
    </insert>

    <!-- 批量查询已存在的记录 -->
    <select id="selectExistKeys" resultType="String">
        SELECT CONCAT(question_group_id, '_', question_id, '_', round_id) AS compositeKey
        FROM paper_question_instance
        WHERE (question_group_id, question_id, round_id) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.questionGroupId}, #{item.questionId}, #{item.roundId})
        </foreach>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO paper_question_instance (
        question_group_id, question_biz_group_id, question_id, question_biz_id,
        round_id, user_score, standard_score, is_scored, is_judged, is_correct, create_by, update_by, enable
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.questionGroupId},
            #{item.questionBizGroupId},
            #{item.questionId},
            #{item.questionBizId},
            #{item.roundId},
            #{item.userScore},
            #{item.standardScore},
            #{item.isScored},
            #{item.isJudged},
            #{item.isCorrect},
            #{item.createBy},
            #{item.updateBy},
            #{item.enable}
            )
        </foreach>
    </insert>

    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE paper_question_instance
            SET
            question_biz_group_id = #{item.questionBizGroupId},
            question_biz_id = #{item.questionBizId},
            user_score = #{item.userScore},
            standard_score = #{item.standardScore},
            is_scored = #{item.isScored},
            is_judged = #{item.isJudged},
            is_correct = #{item.isCorrect},
            update_by = #{item.updateBy},
            enable = #{item.enable}
            WHERE question_group_id = #{item.questionGroupId}
            AND question_id = #{item.questionId}
            AND round_id = #{item.roundId}
        </foreach>
    </update>

    <!-- 根据轮次ID/实例ID查询记录 -->
    <select id="selectPaperQuestionsByRoundId" resultType="PaperQuestionInstancePO">
        SELECT
            pqi.id AS id,
            pqi.question_group_id AS questionGroupId,
            pqi.question_biz_group_id AS questionBizGroupId,
            pqi.question_id AS questionId,
            pqi.question_biz_id AS questionBizId,
            pqi.round_id AS roundId,
            pqi.user_score AS userScore,
            pqi.standard_score AS standardScore,
            pqi.is_scored AS isScored,
            pqi.is_judged AS isJudged,
            pqi.is_correct AS isCorrect,
            pqi.create_time AS createTime,
            pqi.update_time AS updateTime,
            pqi.create_by AS createBy,
            pqi.update_by AS updateBy,
            pqi.enable AS enable,
            qg.type AS questionGroupType
        FROM paper_question_instance pqi
        LEFT JOIN question_group qg ON qg.id = pqi.question_group_id AND qg.enable = 1
        WHERE pqi.enable = 1 AND pqi.round_id = #{roundId}
        ORDER BY pqi.id
    </select>

    <!-- 根据条件查询记录列表 -->
    <select id="selectByRoundIds" parameterType="java.util.List" resultType="PaperQuestionInstancePO">
        SELECT
            pqi.id AS id,
            pqi.question_group_id AS questionGroupId,
            pqi.question_biz_group_id AS questionBizGroupId,
            pqi.question_id AS questionId,
            pqi.question_biz_id AS questionBizId,
            pqi.round_id AS roundId,
            pqi.user_score AS userScore,
            pqi.standard_score AS standardScore,
            pqi.is_scored AS isScored,
            pqi.is_judged AS isJudged,
            pqi.is_correct AS isCorrect,
            pqi.create_time AS createTime,
            pqi.update_time AS updateTime,
            pqi.create_by AS createBy,
            pqi.update_by AS updateBy,
            pqi.enable AS enable,
            qg.type AS questionGroupType
        FROM paper_question_instance pqi
        LEFT JOIN question_group qg ON qg.id = pqi.question_group_id AND qg.enable = 1
        WHERE pqi.round_id IN
        <foreach collection="roundIds" item="roundId" open="(" separator="," close=")">
            #{roundId}
        </foreach>
        AND pqi.enable = 1
        ORDER BY pqi.id
    </select>

    <!-- 根据条件查询用户答题记录 -->
    <select id="getUserAnswerRecord" resultType="PaperQuestionInstancePO">
        SELECT
            pqi.id AS id,
            pqi.question_group_id AS questionGroupId,
            pqi.question_biz_group_id AS questionBizGroupId,
            pqi.question_id AS questionId,
            pqi.question_biz_id AS questionBizId,
            pqi.round_id AS roundId,
            pqi.standard_score AS standardScore,
            pqi.user_score AS userScore,
            pqi.is_scored AS isScored,
            pqi.is_judged AS isJudged,
            pqi.is_correct AS isCorrect,
            pqi.create_time AS createTime
        FROM paper_score_batch psb
        LEFT JOIN paper_round pr ON pr.score_batch_id = psb.id AND pr.enable = 1
        LEFT JOIN paper_question_instance pqi ON pqi.round_id = pr.id AND pqi.enable = 1
        WHERE psb.enable = 1
        AND psb.paper_id = #{paperId}
        <if test="paperVersionNumber != null">
            AND psb.paper_version_number = #{paperVersionNumber}
        </if>
        AND psb.open_id = #{openId}
        AND psb.tenant_id = #{tenantId}
        <if test="status != null">
            AND psb.status = #{status}
        </if>
        ORDER BY pqi.id
    </select>

    <!-- 根据分数批次ID查询用户答题记录 -->
    <select id="getUserAnswerRecordByScoreBatchId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            paper_question_instance pqi
            INNER JOIN paper_round pr ON pqi.round_id = pr.id AND pr.enable = 1
        WHERE
            pqi.enable = 1
            AND pr.score_batch_id = #{scoreBatchId}
    </select>


    <!-- 仅返回回答错误的小题的最小字段集合 -->
    <select id="selectIncorrectByRoundId" parameterType="java.lang.String" resultType="PaperQuestionInstancePO">
        SELECT
            pqi.question_biz_id AS questionBizId,
            qg.type AS questionGroupType,
            pqi.is_correct AS isCorrect
        FROM paper_question_instance pqi
        LEFT JOIN question_group qg ON qg.id = pqi.question_group_id AND qg.enable = 1
        WHERE pqi.enable = 1
          AND pqi.round_id = #{roundId}
          AND pqi.is_correct = 0
        ORDER BY pqi.id
    </select>

</mapper>