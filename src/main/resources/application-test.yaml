spring:
  datasource:
    url: ******************************************************************************************
    username: ipublish_user
    password: vuZGkiPgPDVGUdS4
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      max-active: 500
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
        allow:
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500
    # kafka配置
  kafka:
    bootstrap-servers: ************:9092,************:9092,***********:9092
    consumer:
      group-id: test-ipublish-group
      auto-offset-reset: earliest
      max-poll-records: 1000
# kafka topic配置
kafka:
  topic:
    operationLog: test-ipublish-bookOperationLog
    bookOperationLog: test-ipublish-operation-book
    chapterOperationLog: test-ipublish-operation-chapter
    clioResult: dev_clio_result_2_ipublish
    bookPublishTopic: test-ipublish-bookPublished
    userAction: test-ipublish-userAction
    uaiResourcePublish: test_resource_publish
    customContent: test-ipublish-custom-content-Published
    tenantMessageSaveTopic: test-ipublish-tenantMessageSave
    tenantMessageDeleteTopic: test-ipublish-tenantMessageDelete
    useBookTopic: test-ipublish-use-book
    wordPracticePublishTopic: test-ipublish-wordPracticePublished
    readingTime: test-ipublish-readingTime
app:
  env: test

redisson:
  nodes:
    - redis-6d67dc5e-555f-42ea-b0e8-07d0ab1dafaf.cn-north-4.dcs.myhuaweicloud.com:6379
  max-redirects: 3
  connection-timeout: 1000
  read-timeout: 1000
  scan-interval: 30

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /doc/swagger-ui.html

remote:
  sso:
    url: https://utsso.unipus.cn
    serviceName: https://ipublish-test.unipus.cn
  aigc:
    url: https://llm-api.unipus.cn
    sk: sk-dp4e1ZnsS8uWKDM712289cE764C14f5aB794C2Cf2596B8Aa
  qrcode:
    url: https://testucontent-cms.unipus.cn
  soe:
    url: https://soetest.unipus.cn
    appKey: ipublish
    appSecret: EiBBCfPzEMHvLQnN
  engine:
    appKey: iPublish
    appSecret: UmGZLkXti2fg
    host: https://engine-huawei-test.unipus.cn
  translate:
    appKey: iPublish
    appSecret: uZxZq5HaKmUiji04207yZmff30z9vg8A
  ucontent:
    host: http://ucontent-api-v3.ucontent-test:80
  knowledge:
    host: http://************:30447/mindmap
    resourceLinkUrl: https://ucloud-test-hw.unipus.cn/api/tla/courseStudy/knowledge/link
  assistant:
    host: http://assistant-api.ucontent-test:8080
    appKey: testOnly20240529
    appSecret: testOnly20240529testOnly20240529
  wordpractice:
    host: https://ucloud-test-hw.unipus.cn
    secret: mySuperSecretKeyWithAtLeast32CharactersLong

grpc:
  server:
    question:
      host: mid-qs-api-test.api1024.cn
      port: 30080

logging:
  level:
    com.unipus: INFO

jwt:
  secret: QW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgYXR0ZW1wdGluZyB0byBlbmNvZGUgdGhlIEp3dDogRmFpbGVkIHRvIHNlbGVjdCBhIEpXSw==
  # token过期时间（天）
  expiration: 30
  # 协作token密钥
  coSecret: 5LiN5piv5ZCn5LiN5piv5ZCn77yM6L+Z5LmI54K55bel6LWE6L+Y5oOz5bmy5ZWl5ZGi
  # 协作token过期时间（天）
  coExpiration: 30

white-list:
  # 登录校验白名单
  security-urls:
    - /auth/validate
    - /auth/accessToken
    - /doc/swagger-ui/index.html
    - /doc/swagger-ui/*
    - /v3/api-docs/*
    - /v3/api-docs
    - /engine/writeCorrectCallback
  # 权限校验白名单
  permission-urls:
    - /reader/**
    - /auth/validate
    - /auth/accessToken
    - /v3/api-docs/**
    - /doc/swagger-ui/**
    - /doc/convert-word-to-html
    - /engine/writeCorrectCallback
    - /cos/getCredential
    - /cos/getCOSCredential
    - /cos/answer/getCOSCredential
    - /cos/checkMediaTranscodeJob
    - /cos/createMediaTranscodeJob
    - /cos/getMediaTranscodeJob
    - /cos/createVideoMediaTranscodeJob
  # 内网调用白名单
  internal-urls:
    - /backend/**
  # 内网IP网段
  internal-ip-ranges:
    - **********/16
    - *************/32
    - ***********/32
    - **************/32
  # 日志过滤白名单
  log-filter-exclude-urls:
    - /health
    - /metrics
    - /api/v3/api-docs
    - /api/doc/swagger-ui/
    - /api/druid/*

#测试环境腾讯云cos配置
tencent:
  cos:
    secret-id: AKID1vd9XkCdrtJZeaLwKftujRzNekjW9aQ8
    secret-key: d5NUO5av53rTs43qRq47yb1EH9qLekcH
    bucket: ipublish-test-**********
    region: ap-beijing

# 飞书机器人配置
feishu:
  bot:
    # 飞书机器人Webhook地址
    webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/be54aa3d-e7f2-45d8-bbf7-693b91ed87e5
    # 飞书机器人密钥（用于签名验证）
    secret:
    # 是否启用飞书机器人
    enabled: true
    # 请求超时时间（毫秒）
    timeout: 10000
    # Grafana基础URL
    grafana-base-url: http://************:31588
    # Grafana数据源UID
    grafana-datasource-uid: ab10533a-937d-40e0-bb29-cb9ff814fe1a
    # Grafana应用名称（用于日志查询）
    grafana-app-name: digitalbook
    # Grafana查询时间范围（从当前时间往前推）
    grafana-time-range: now-2h
    # Grafana查询时间范围结束（默认到当前时间）
    grafana-time-range-to: now
