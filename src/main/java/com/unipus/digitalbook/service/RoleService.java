package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.ApiListDTO;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.entity.role.RolePermission;
import com.unipus.digitalbook.model.entity.role.RoleSearchList;
import com.unipus.digitalbook.model.entity.role.RoleUser;
import com.unipus.digitalbook.model.enums.RolePermissionTypeEnum;
import com.unipus.digitalbook.model.po.role.RoleUserRelationPO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 业务逻辑层接口，提供角色相关操作。
 */
public interface RoleService {

    /**
     * 保存角色（新增或更新）。
     *
     * @param role 业务逻辑层角色对象
     * @return 菜单id
     */
    Long save(Role role);

    /**
     * 删除角色
     * @param roleId 角色ID
     * @param opUserId 操作人ID
     * @return 是否删除成功
     */
    boolean delete(Long opUserId, Long roleId);

    /**
     * 更新角色状态
     * @param opUserId 操作人ID
     * @param roleId   角色ID
     * @param status   角色状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long opUserId, Long roleId, Integer status);

    /**
     * 根据角色ID获取角色信息
     *
     * @param id 角色ID
     * @return 角色对象
     */
    Role getRoleById(Long id);

    /**
     * 获取组织下的所有角色列表
     *
     * @return 角色对象列表
     */
    List<Role> orgAssignedRoleList(Long orgId);

    /**
     * 获取用户已分配的角色列表
     *
     * @return 角色对象列表
     */
    List<Role> userAssignedRoleList(Long userId, Long orgId);

    /**
     * 根据条件搜索角色
     * @param name 角色名称
     * @param status 角色状态
     * @return 符合条件的角色对象列表
     */
    RoleSearchList search(String name, Integer status, int offset, int limit);

    /**
     * 给组织分配角色
     * @param roleIds 角色ID 如果角色id是空则删除组织下所有的id
     * @param orgId 组织ID
     * @param opUserId 操作人ID
     * @return 是否分配成功
     */
    boolean bathAssignRole2Org(List<Long> roleIds, Long orgId, Long opUserId);

    /**
     * 给角色分配权限
     * @param rolePermission 权限分配参数对象 {@link RolePermission}
     * @return 返回一个布尔值，表示权限分配是否成功
     */
    boolean assignRolePermission(RolePermission rolePermission, RolePermissionTypeEnum type, Long opUserId);

    /**
     * 获取角色的接口权限
     *
     * @param roleIds 角色ID列表
     * @return 角色的接口权限, 包括角色分配的状态
     */
    ApiListDTO getInterfaceList(List<Long> roleIds);

    /**
     * 获取角色已分配的权限
     * @param roleIds 角色ID列表
     * @param type 权限类型
     * @return 角色已分配的权限集合
     */
    Set<String> getAssignedPermissions(List<Long> roleIds, RolePermissionTypeEnum type);

    /**
     *  根据用户id取得用户组织角色关系
     * @param userId 用户id
     * @return List<RoleUserRelationPO>
     */
    List<RoleUserRelationPO> getByUserId(Long userId);

    /**
     * 分配角色给用户
     * @param roleUser 分配角色参数对象
     * @param opUserId 操作人ID
     * @return 是否分配成功
     */
    boolean bathAssignRole2User(RoleUser roleUser, Long opUserId);

    /**
     * 根据组织ID获取角色列表
     *
     * @param orgId 组织ID
     * @return 角色列表
     */
    List<Role> getByOrgId(Long orgId);

    /**
     * 获取角色权限列表
     *
     * @return 角色权限映射
     */
    Map<Long, Set<String>> getAllPermissions();

    /**
     * 获取权限角色列表
     * @param roleIds 角色ID列表
     * @return 角色权限列表
     */
    Set<String> getPermissionsByRoles(Set<Long> roleIds);
}