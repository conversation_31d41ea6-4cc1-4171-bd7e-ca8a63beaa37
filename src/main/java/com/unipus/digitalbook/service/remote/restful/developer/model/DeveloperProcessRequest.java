package com.unipus.digitalbook.service.remote.restful.developer.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@AllArgsConstructor
@Data
public class DeveloperProcessRequest {
    private String env;
    private String requestId;
    private String description;

    private String logUrl;
    private String requestUrl;

    private String exceptionMessage;
    private String exceptionDetail;


    /**
     * 构造函数，用于初始化DeveloperProcessRequest对象。
     *
     * @param env         开发环境标识
     * @param requestId   请求唯一标识符
     * @param description 请求描述信息
     * @param logUrl      日志文件URL
     * @param requestUrl  请求URL
     * @param exception   异常对象，如果存在的话
     */
    public DeveloperProcessRequest(String env, String requestId, String description, String logUrl, String requestUrl, Exception exception) {
        // 初始化环境标识
        this.env = env;
        // 初始化请求唯一标识符
        this.requestId = requestId;
        // 初始化请求描述信息
        this.description = description;
        // 初始化日志文件URL
        this.logUrl = logUrl;
        // 初始化请求URL
        this.requestUrl = requestUrl;
        // 检查是否存在异常对象
        if (exception != null) {
            // 存在异常时，记录异常消息
            this.exceptionMessage = exception.getMessage();
            // 记录异常详细信息
            this.exceptionDetail = exception.toString();
        } else {
            // 不存在异常时，将异常消息和详细信息设为null
            this.exceptionMessage = null;
            this.exceptionDetail = null;
        }
    }


}
