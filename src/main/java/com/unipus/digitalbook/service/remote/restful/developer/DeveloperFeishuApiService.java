package com.unipus.digitalbook.service.remote.restful.developer;

import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.model.entity.tenant.TenantMessage;
import com.unipus.digitalbook.service.remote.restful.developer.model.DeveloperProcessRequest;
import com.unipus.digitalbook.service.remote.restful.developer.model.DeveloperSyncRequest;
import com.unipus.digitalbook.service.remote.restful.ucontent.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/base/automation/webhook/event/")
public interface DeveloperFeishuApiService {

    @PostExchange(value = "AnsCa75klwGA3shYlEFcHnS1nxd", contentType = "application/json")
    BaseResponse<JSONObject> syncFail(@RequestBody DeveloperSyncRequest request);


    @PostExchange(value = "AZW1aT7OdwT5CkhGhVecSaxFn9g", contentType = "application/json")
    BaseResponse<JSONObject> processException(@RequestBody DeveloperProcessRequest request);
}
