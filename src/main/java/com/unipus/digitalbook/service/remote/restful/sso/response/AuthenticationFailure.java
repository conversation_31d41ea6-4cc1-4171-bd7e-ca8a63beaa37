package com.unipus.digitalbook.service.remote.restful.sso.response;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlValue;

@XmlAccessorType(XmlAccessType.FIELD)
public class AuthenticationFailure {

    @XmlAttribute(name = "code")
    private String code;

    @XmlValue
    private String message;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}